package com.andyworks.simpleweather.graphics

import android.content.Context
import android.opengl.GLES20
import android.opengl.Matrix
import com.andyworks.simpleweather.data.model.ThemeType
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import kotlin.math.cos
import kotlin.math.sin

class SunRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private lateinit var vertexBuffer: FloatBuffer
    private lateinit var indexBuffer: ByteBuffer
    
    private val modelMatrix = FloatArray(16)
    private var rotationAngle = 0f
    private var currentTheme = ThemeType.NORMAL
    
    // Sun geometry - circle with rays
    private val sunVertices = mutableListOf<Float>()
    private val sunIndices = mutableListOf<Byte>()
    
    private val vertexShaderCode = """
        uniform mat4 uMVPMatrix;
        attribute vec4 vPosition;
        void main() {
            gl_Position = uMVPMatrix * vPosition;
        }
    """.trimIndent()
    
    private val fragmentShaderCode = """
        precision mediump float;
        uniform vec4 vColor;
        void main() {
            gl_FragColor = vColor;
        }
    """.trimIndent()
    
    fun initialize() {
        createSunGeometry()
        setupBuffers()
        
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode)
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType) {
        // Update rotation
        rotationAngle += 0.5f
        if (rotationAngle > 360f) rotationAngle -= 360f
        
        // Set up model matrix with rotation
        Matrix.setIdentityM(modelMatrix, 0)
        Matrix.rotateM(modelMatrix, 0, rotationAngle, 0f, 0f, 1f)
        
        val finalMvpMatrix = FloatArray(16)
        Matrix.multiplyMM(finalMvpMatrix, 0, mvpMatrix, 0, modelMatrix, 0)
        
        GLES20.glUseProgram(program)
        
        // Enable vertex array
        GLES20.glEnableVertexAttribArray(positionHandle)
        
        // Prepare the coordinate data
        GLES20.glVertexAttribPointer(
            positionHandle, 3,
            GLES20.GL_FLOAT, false,
            0, vertexBuffer
        )
        
        // Set color based on theme
        val sunColor = getSunColor(theme)
        GLES20.glUniform4fv(colorHandle, 1, sunColor, 0)
        
        // Apply the projection and view transformation
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, finalMvpMatrix, 0)
        
        // Draw the sun
        GLES20.glDrawElements(
            GLES20.GL_TRIANGLES, sunIndices.size,
            GLES20.GL_UNSIGNED_BYTE, indexBuffer
        )
        
        // Disable vertex array
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    fun updateTheme(theme: ThemeType) {
        currentTheme = theme
    }
    
    fun onTouch(x: Float, y: Float) {
        // Add touch interaction - maybe make sun pulse or change color
        // This could trigger sound effects in the audio system
    }
    
    private fun createSunGeometry() {
        sunVertices.clear()
        sunIndices.clear()
        
        val radius = 0.3f
        val rayLength = 0.15f
        val segments = 16
        val rays = 8
        
        // Center point
        sunVertices.addAll(listOf(0f, 0f, 0f))
        
        // Circle vertices
        for (i in 0 until segments) {
            val angle = 2.0 * Math.PI * i / segments
            val x = (radius * cos(angle)).toFloat()
            val y = (radius * sin(angle)).toFloat()
            sunVertices.addAll(listOf(x, y, 0f))
        }
        
        // Ray vertices
        for (i in 0 until rays) {
            val angle = 2.0 * Math.PI * i / rays
            val innerX = ((radius + 0.05f) * cos(angle)).toFloat()
            val innerY = ((radius + 0.05f) * sin(angle)).toFloat()
            val outerX = ((radius + rayLength) * cos(angle)).toFloat()
            val outerY = ((radius + rayLength) * sin(angle)).toFloat()
            
            sunVertices.addAll(listOf(innerX, innerY, 0f))
            sunVertices.addAll(listOf(outerX, outerY, 0f))
        }
        
        // Circle indices
        for (i in 0 until segments) {
            sunIndices.addAll(listOf(0, (i + 1).toByte(), ((i + 1) % segments + 1).toByte()))
        }
        
        // Ray indices
        val rayStartIndex = segments + 1
        for (i in 0 until rays) {
            val baseIndex = rayStartIndex + i * 2
            sunIndices.addAll(listOf(
                baseIndex.toByte(),
                (baseIndex + 1).toByte(),
                (baseIndex + 2).toByte()
            ))
            sunIndices.addAll(listOf(
                baseIndex.toByte(),
                (baseIndex + 2).toByte(),
                (baseIndex + 3).toByte()
            ))
        }
    }
    
    private fun setupBuffers() {
        val vertexArray = sunVertices.toFloatArray()
        val indexArray = sunIndices.toByteArray()
        
        vertexBuffer = ByteBuffer.allocateDirect(vertexArray.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply {
                put(vertexArray)
                position(0)
            }
        }
        
        indexBuffer = ByteBuffer.allocateDirect(indexArray.size).apply {
            put(indexArray)
            position(0)
        }
    }
    
    private fun getSunColor(theme: ThemeType): FloatArray {
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(1.0f, 0.7f, 0.0f, 1.0f) // Orange
            ThemeType.CHROMA -> floatArrayOf(1.0f, 0.2f, 0.8f, 1.0f) // Pink
            ThemeType.DEPTH -> floatArrayOf(1.0f, 1.0f, 0.0f, 1.0f) // Yellow
            ThemeType.KARAT -> floatArrayOf(1.0f, 0.8f, 0.0f, 1.0f) // Gold
            else -> floatArrayOf(1.0f, 1.0f, 0.0f, 1.0f) // Default yellow
        }
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}
