package com.andyworks.simpleweather.graphics

import android.content.Context
import android.opengl.GLES20
import android.opengl.GLSurfaceView
import android.opengl.Matrix
import com.andyworks.simpleweather.data.model.WeatherCode
import com.andyworks.simpleweather.data.model.ThemeType
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class WeatherRenderer(
    private val context: Context
) : GLSurfaceView.Renderer {
    
    private val mvpMatrix = FloatArray(16)
    private val projectionMatrix = FloatArray(16)
    private val viewMatrix = FloatArray(16)
    private val modelMatrix = FloatArray(16)
    
    private var weatherScene: WeatherScene? = null
    private var currentWeatherCode: WeatherCode = WeatherCode.CLEAR
    private var currentTheme: ThemeType = ThemeType.NORMAL
    
    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        // Set the background color
        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 1.0f)
        
        // Enable depth testing
        GLES20.glEnable(GLES20.GL_DEPTH_TEST)
        GLES20.glDepthFunc(GLES20.GL_LEQUAL)
        
        // Enable blending for transparency
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        
        // Initialize the weather scene
        weatherScene = WeatherScene(context)
        weatherScene?.initialize()
    }
    
    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        GLES20.glViewport(0, 0, width, height)
        
        val ratio: Float = width.toFloat() / height.toFloat()
        
        // Set up the projection matrix
        Matrix.frustumM(projectionMatrix, 0, -ratio, ratio, -1f, 1f, 3f, 7f)
    }
    
    override fun onDrawFrame(gl: GL10?) {
        // Clear the screen
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
        
        // Set up the camera position
        Matrix.setLookAtM(viewMatrix, 0, 0f, 0f, 3f, 0f, 0f, 0f, 0f, 1.0f, 0.0f)
        
        // Calculate the projection and view transformation
        Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, viewMatrix, 0)
        
        // Draw the weather scene
        weatherScene?.draw(mvpMatrix, currentWeatherCode, currentTheme)
    }
    
    fun updateWeather(weatherCode: WeatherCode) {
        currentWeatherCode = weatherCode
    }
    
    fun updateTheme(theme: ThemeType) {
        currentTheme = theme
        weatherScene?.updateTheme(theme)
    }
    
    fun onTouch(x: Float, y: Float) {
        weatherScene?.onTouch(x, y)
    }
}

class WeatherScene(private val context: Context) {
    
    private var sunRenderer: SunRenderer? = null
    private var moonRenderer: MoonRenderer? = null
    private var cloudRenderer: CloudRenderer? = null
    private var rainRenderer: RainRenderer? = null
    private var snowRenderer: SnowRenderer? = null
    private var lightningRenderer: LightningRenderer? = null
    private var particleSystem: ParticleSystem? = null
    
    fun initialize() {
        sunRenderer = SunRenderer(context)
        moonRenderer = MoonRenderer(context)
        cloudRenderer = CloudRenderer(context)
        rainRenderer = RainRenderer(context)
        snowRenderer = SnowRenderer(context)
        lightningRenderer = LightningRenderer(context)
        particleSystem = ParticleSystem(context)
        
        sunRenderer?.initialize()
        moonRenderer?.initialize()
        cloudRenderer?.initialize()
        rainRenderer?.initialize()
        snowRenderer?.initialize()
        lightningRenderer?.initialize()
        particleSystem?.initialize()
    }
    
    fun draw(mvpMatrix: FloatArray, weatherCode: WeatherCode, theme: ThemeType) {
        val isDaytime = isDaytime()
        
        // Draw background elements first
        if (isDaytime) {
            sunRenderer?.draw(mvpMatrix, theme)
        } else {
            moonRenderer?.draw(mvpMatrix, theme)
        }
        
        // Draw weather-specific elements
        when (weatherCode) {
            WeatherCode.CLEAR, WeatherCode.MOSTLY_CLEAR -> {
                // Just sun/moon, no additional elements
            }
            WeatherCode.PARTLY_CLOUDY, WeatherCode.MOSTLY_CLOUDY, WeatherCode.CLOUDY -> {
                cloudRenderer?.draw(mvpMatrix, theme, getCloudDensity(weatherCode))
            }
            WeatherCode.RAIN, WeatherCode.RAIN_LIGHT, WeatherCode.RAIN_HEAVY -> {
                cloudRenderer?.draw(mvpMatrix, theme, 0.8f)
                rainRenderer?.draw(mvpMatrix, theme, getRainIntensity(weatherCode))
            }
            WeatherCode.SNOW, WeatherCode.SNOW_LIGHT, WeatherCode.SNOW_HEAVY -> {
                cloudRenderer?.draw(mvpMatrix, theme, 0.9f)
                snowRenderer?.draw(mvpMatrix, theme, getSnowIntensity(weatherCode))
            }
            WeatherCode.THUNDERSTORM -> {
                cloudRenderer?.draw(mvpMatrix, theme, 1.0f)
                rainRenderer?.draw(mvpMatrix, theme, 0.8f)
                lightningRenderer?.draw(mvpMatrix, theme)
            }
            WeatherCode.FOG, WeatherCode.FOG_LIGHT -> {
                particleSystem?.drawFog(mvpMatrix, theme, getFogDensity(weatherCode))
            }
            else -> {
                cloudRenderer?.draw(mvpMatrix, theme, 0.5f)
            }
        }
        
        // Update particle systems
        particleSystem?.update()
    }
    
    fun updateTheme(theme: ThemeType) {
        sunRenderer?.updateTheme(theme)
        moonRenderer?.updateTheme(theme)
        cloudRenderer?.updateTheme(theme)
        rainRenderer?.updateTheme(theme)
        snowRenderer?.updateTheme(theme)
        lightningRenderer?.updateTheme(theme)
        particleSystem?.updateTheme(theme)
    }
    
    fun onTouch(x: Float, y: Float) {
        // Handle touch interactions for weather elements
        sunRenderer?.onTouch(x, y)
        moonRenderer?.onTouch(x, y)
        cloudRenderer?.onTouch(x, y)
    }
    
    private fun isDaytime(): Boolean {
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        return hour in 6..18
    }
    
    private fun getCloudDensity(weatherCode: WeatherCode): Float {
        return when (weatherCode) {
            WeatherCode.PARTLY_CLOUDY -> 0.3f
            WeatherCode.MOSTLY_CLOUDY -> 0.6f
            WeatherCode.CLOUDY -> 0.9f
            else -> 0.5f
        }
    }
    
    private fun getRainIntensity(weatherCode: WeatherCode): Float {
        return when (weatherCode) {
            WeatherCode.RAIN_LIGHT -> 0.3f
            WeatherCode.RAIN -> 0.6f
            WeatherCode.RAIN_HEAVY -> 1.0f
            else -> 0.5f
        }
    }
    
    private fun getSnowIntensity(weatherCode: WeatherCode): Float {
        return when (weatherCode) {
            WeatherCode.SNOW_LIGHT -> 0.3f
            WeatherCode.SNOW -> 0.6f
            WeatherCode.SNOW_HEAVY -> 1.0f
            else -> 0.5f
        }
    }
    
    private fun getFogDensity(weatherCode: WeatherCode): Float {
        return when (weatherCode) {
            WeatherCode.FOG_LIGHT -> 0.4f
            WeatherCode.FOG -> 0.8f
            else -> 0.6f
        }
    }
}
