package com.andyworks.simpleweather.data.repository

import com.andyworks.simpleweather.data.api.WeatherApiService
import com.andyworks.simpleweather.data.api.OpenWeatherMapService
import com.andyworks.simpleweather.data.api.AccuWeatherService
import com.andyworks.simpleweather.data.model.WeatherResponse
import com.andyworks.simpleweather.data.model.LocationData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WeatherRepository @Inject constructor(
    private val weatherApiService: WeatherApiService,
    private val openWeatherMapService: OpenWeatherMapService,
    private val accuWeatherService: AccuWeatherService,
    private val preferencesRepository: PreferencesRepository
) {
    
    suspend fun getCurrentWeather(location: LocationData): Flow<Result<WeatherResponse>> = flow {
        try {
            emit(Result.Loading)
            
            val dataSource = preferencesRepository.getSelectedDataSource()
            val units = preferencesRepository.getTemperatureUnit()
            
            val response = when (dataSource) {
                DataSource.ANDY_WORKS -> {
                    weatherApiService.getCurrentWeather(
                        latitude = location.latitude,
                        longitude = location.longitude,
                        units = units
                    )
                }
                DataSource.OPEN_WEATHER_MAP -> {
                    // Convert OpenWeatherMap response to our format
                    val owmResponse = openWeatherMapService.getCurrentWeather(
                        latitude = location.latitude,
                        longitude = location.longitude,
                        apiKey = getOpenWeatherMapApiKey(),
                        units = units
                    )
                    // TODO: Convert OWM response to WeatherResponse format
                    null
                }
                DataSource.ACCUWEATHER -> {
                    // Convert AccuWeather response to our format
                    // TODO: Implement AccuWeather conversion
                    null
                }
            }
            
            if (response?.isSuccessful == true) {
                response.body()?.let { weatherData ->
                    emit(Result.Success(weatherData))
                } ?: emit(Result.Error("No weather data received"))
            } else {
                emit(Result.Error("Failed to fetch weather data: ${response?.message()}"))
            }
            
        } catch (e: Exception) {
            emit(Result.Error("Network error: ${e.message}"))
        }
    }
    
    suspend fun getWeatherForecast(location: LocationData): Flow<Result<WeatherResponse>> = flow {
        try {
            emit(Result.Loading)
            
            val dataSource = preferencesRepository.getSelectedDataSource()
            val units = preferencesRepository.getTemperatureUnit()
            
            val response = weatherApiService.getWeatherForecast(
                latitude = location.latitude,
                longitude = location.longitude,
                days = 7,
                units = units
            )
            
            if (response.isSuccessful) {
                response.body()?.let { forecastData ->
                    emit(Result.Success(forecastData))
                } ?: emit(Result.Error("No forecast data received"))
            } else {
                emit(Result.Error("Failed to fetch forecast: ${response.message()}"))
            }
            
        } catch (e: Exception) {
            emit(Result.Error("Network error: ${e.message}"))
        }
    }
    
    suspend fun getHourlyForecast(location: LocationData): Flow<Result<WeatherResponse>> = flow {
        try {
            emit(Result.Loading)
            
            val units = preferencesRepository.getTemperatureUnit()
            
            val response = weatherApiService.getHourlyForecast(
                latitude = location.latitude,
                longitude = location.longitude,
                hours = 24,
                units = units
            )
            
            if (response.isSuccessful) {
                response.body()?.let { hourlyData ->
                    emit(Result.Success(hourlyData))
                } ?: emit(Result.Error("No hourly data received"))
            } else {
                emit(Result.Error("Failed to fetch hourly forecast: ${response.message()}"))
            }
            
        } catch (e: Exception) {
            emit(Result.Error("Network error: ${e.message}"))
        }
    }
    
    private fun getOpenWeatherMapApiKey(): String {
        // In a real app, this would be stored securely
        return "YOUR_OPENWEATHERMAP_API_KEY"
    }
    
    private fun getAccuWeatherApiKey(): String {
        // In a real app, this would be stored securely
        return "YOUR_ACCUWEATHER_API_KEY"
    }
}

sealed class Result<out T> {
    object Loading : Result<Nothing>()
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val message: String) : Result<Nothing>()
}

enum class DataSource {
    ANDY_WORKS,
    OPEN_WEATHER_MAP,
    ACCUWEATHER
}
