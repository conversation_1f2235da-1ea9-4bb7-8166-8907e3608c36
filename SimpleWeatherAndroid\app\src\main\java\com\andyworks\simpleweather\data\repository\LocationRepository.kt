package com.andyworks.simpleweather.data.repository

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Geocoder
import android.location.Location
import androidx.core.content.ContextCompat
import com.andyworks.simpleweather.data.model.LocationData
import com.andyworks.simpleweather.data.model.LocationResult
import com.andyworks.simpleweather.data.model.LocationSearchResult
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class LocationRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val fusedLocationClient: FusedLocationProviderClient = 
        LocationServices.getFusedLocationProviderClient(context)
    
    private val geocoder = Geocoder(context, Locale.getDefault())
    
    suspend fun getCurrentLocation(): Flow<LocationResult> = flow {
        emit(LocationResult.Loading)
        
        if (!hasLocationPermission()) {
            emit(LocationResult.PermissionDenied)
            return@flow
        }
        
        try {
            val location = getCurrentLocationInternal()
            if (location != null) {
                val locationData = convertToLocationData(location)
                emit(LocationResult.Success(locationData))
            } else {
                emit(LocationResult.Error("Unable to get current location"))
            }
        } catch (e: Exception) {
            emit(LocationResult.Error("Location error: ${e.message}"))
        }
    }
    
    suspend fun searchLocations(query: String): List<LocationSearchResult> {
        return try {
            val addresses = geocoder.getFromLocationName(query, 5)
            addresses?.map { address ->
                LocationSearchResult(
                    name = address.locality ?: address.subAdminArea ?: address.adminArea ?: "Unknown",
                    country = address.countryName ?: "Unknown",
                    state = address.adminArea,
                    latitude = address.latitude,
                    longitude = address.longitude
                )
            } ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getLocationFromCoordinates(latitude: Double, longitude: Double): LocationData? {
        return try {
            val addresses = geocoder.getFromLocation(latitude, longitude, 1)
            addresses?.firstOrNull()?.let { address ->
                LocationData(
                    latitude = latitude,
                    longitude = longitude,
                    name = address.locality ?: address.subAdminArea ?: address.adminArea ?: "Unknown Location",
                    country = address.countryName ?: "Unknown",
                    state = address.adminArea,
                    isCurrentLocation = false
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    private fun hasLocationPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private suspend fun getCurrentLocationInternal(): Location? = suspendCancellableCoroutine { continuation ->
        if (!hasLocationPermission()) {
            continuation.resume(null)
            return@suspendCancellableCoroutine
        }
        
        val cancellationTokenSource = CancellationTokenSource()
        
        continuation.invokeOnCancellation {
            cancellationTokenSource.cancel()
        }
        
        try {
            fusedLocationClient.getCurrentLocation(
                Priority.PRIORITY_HIGH_ACCURACY,
                cancellationTokenSource.token
            ).addOnSuccessListener { location ->
                continuation.resume(location)
            }.addOnFailureListener { exception ->
                continuation.resume(null)
            }
        } catch (e: SecurityException) {
            continuation.resume(null)
        }
    }
    
    private suspend fun convertToLocationData(location: Location): LocationData {
        val locationData = getLocationFromCoordinates(location.latitude, location.longitude)
        return locationData?.copy(isCurrentLocation = true) ?: LocationData(
            latitude = location.latitude,
            longitude = location.longitude,
            name = "Current Location",
            country = "Unknown",
            state = null,
            isCurrentLocation = true
        )
    }
}
