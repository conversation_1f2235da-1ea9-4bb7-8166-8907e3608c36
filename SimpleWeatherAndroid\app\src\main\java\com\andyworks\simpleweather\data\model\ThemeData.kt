package com.andyworks.simpleweather.data.model

import kotlinx.serialization.Serializable

@Serializable
data class WeatherTheme(
    val id: String,
    val name: String,
    val sku: String,
    val description: String,
    val edition: String,
    val productUrl: String,
    val firstAppVersion: String,
    val isNew: Boolean,
    val characterPairSpacing: List<CharacterSpacing>,
    val baseThemeId: String,
    val baseNumbersThemeId: String
)

@Serializable
data class CharacterSpacing(
    val c1: String,
    val c2: String,
    val spacing: Double
)

@Serializable
data class ThemeColors(
    val colors: ColorDefinitions,
    val codes: Map<String, String>,
    val icons: Map<String, List<IconDefinition>>,
    val widgets: WidgetSettings
)

@Serializable
data class ColorDefinitions(
    val palette: Map<String, String>,
    val elements: Map<String, Map<String, String>>
)

@Serializable
data class IconDefinition(
    val name: String,
    val iconSet: List<String>? = null,
    val colorScheme: Map<String, String>,
    val solar: List<String>
)

@Serializable
data class WidgetSettings(
    val backgroundResizingMode: String,
    val defaultColorScheme: String
)

@Serializable
data class ThemeAnimation(
    val animations: Map<String, AnimationConfig>
)

@Serializable
data class AnimationConfig(
    val duration: Double,
    val easing: String,
    val properties: Map<String, Any>
)

@Serializable
data class AppConfig(
    val cloudConfig: CloudConfig
)

@Serializable
data class CloudConfig(
    val xScale: Double,
    val yScale: Double,
    val zScale: Double,
    val globalYOffset: Double,
    val defaultSphereRadius: Double,
    val distanceScale: List<Double>,
    val sizeScale: List<Double>,
    val speed: List<Double>,
    val xOffset: List<Double>,
    val yOffset: List<Double>,
    val zOffset: List<Double>,
    val windSpeedMultiplier: Double,
    val duration: Double,
    val yOffsetIntervals: List<Double>,
    val numberOfSpheres: List<Int>
)

// Available themes enum
enum class ThemeType(val id: String, val displayName: String) {
    ANDY("andy", "Andy"),
    BRICKS("bricks", "Bricks"),
    CEDAR("cedar", "Cedar"),
    CHROMA("chroma", "Chroma"),
    DEPTH("depth", "Depth"),
    GRAPHITE("graphite", "Graphite"),
    KARAT("karat", "Karat"),
    MONSTER("monster", "Monster"),
    NORMAL("normal", "Normal"),
    OPAL("opal", "Opal"),
    PRESSTUBE("presstube", "Presstube"),
    WIREFRAME("wireframe", "Wireframe");

    companion object {
        fun fromId(id: String): ThemeType? {
            return values().find { it.id == id }
        }
    }
}

// Color scheme modes
enum class ColorScheme {
    LIGHT,
    DARK,
    NO_BACKGROUND
}

// Solar time for icon selection
enum class SolarTime {
    DAY,
    NIGHT
}
