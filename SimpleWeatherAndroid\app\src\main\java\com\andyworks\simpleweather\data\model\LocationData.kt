package com.andyworks.simpleweather.data.model

import kotlinx.serialization.Serializable

@Serializable
data class LocationData(
    val latitude: Double,
    val longitude: Double,
    val name: String,
    val country: String,
    val state: String? = null,
    val isCurrentLocation: Boolean = false
)

@Serializable
data class LocationSearchResult(
    val name: String,
    val country: String,
    val state: String? = null,
    val latitude: Double,
    val longitude: Double,
    val population: Int? = null
)

data class LocationPermissionState(
    val isGranted: Boolean,
    val shouldShowRationale: Boolean,
    val isPermanentlyDenied: Boolean
)

sealed class LocationResult {
    data class Success(val location: LocationData) : LocationResult()
    data class Error(val message: String) : LocationResult()
    object Loading : LocationResult()
    object PermissionDenied : LocationResult()
}
