package com.andyworks.simpleweather.graphics

import android.content.Context
import android.opengl.GLES20
import android.opengl.Matrix
import com.andyworks.simpleweather.data.model.ThemeType
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

// Moon Renderer
class MoonRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private lateinit var vertexBuffer: FloatBuffer
    private lateinit var indexBuffer: ByteBuffer
    
    private val modelMatrix = FloatArray(16)
    private var currentTheme = ThemeType.NORMAL
    
    fun initialize() {
        createMoonGeometry()
        setupShaders()
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType) {
        Matrix.setIdentityM(modelMatrix, 0)
        Matrix.translateM(modelMatrix, 0, 0.5f, 0.5f, 0f)
        
        val finalMvpMatrix = FloatArray(16)
        Matrix.multiplyMM(finalMvpMatrix, 0, mvpMatrix, 0, modelMatrix, 0)
        
        GLES20.glUseProgram(program)
        GLES20.glEnableVertexAttribArray(positionHandle)
        
        GLES20.glVertexAttribPointer(positionHandle, 3, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        
        val moonColor = getMoonColor(theme)
        GLES20.glUniform4fv(colorHandle, 1, moonColor, 0)
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, finalMvpMatrix, 0)
        
        GLES20.glDrawElements(GLES20.GL_TRIANGLES, 48, GLES20.GL_UNSIGNED_BYTE, indexBuffer)
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    fun updateTheme(theme: ThemeType) { currentTheme = theme }
    fun onTouch(x: Float, y: Float) { /* Moon touch interaction */ }
    
    private fun createMoonGeometry() {
        val vertices = mutableListOf<Float>()
        val indices = mutableListOf<Byte>()
        val radius = 0.25f
        val segments = 16
        
        vertices.addAll(listOf(0f, 0f, 0f)) // Center
        
        for (i in 0 until segments) {
            val angle = 2.0 * Math.PI * i / segments
            val x = (radius * cos(angle)).toFloat()
            val y = (radius * sin(angle)).toFloat()
            vertices.addAll(listOf(x, y, 0f))
        }
        
        for (i in 0 until segments) {
            indices.addAll(listOf(0, (i + 1).toByte(), ((i + 1) % segments + 1).toByte()))
        }
        
        setupBuffers(vertices.toFloatArray(), indices.toByteArray())
    }
    
    private fun getMoonColor(theme: ThemeType): FloatArray {
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(1.0f, 0.9f, 0.7f, 1.0f)
            ThemeType.DEPTH -> floatArrayOf(0.8f, 0.8f, 1.0f, 1.0f)
            else -> floatArrayOf(0.9f, 0.9f, 0.9f, 1.0f)
        }
    }
    
    private fun setupBuffers(vertices: FloatArray, indices: ByteArray) {
        vertexBuffer = ByteBuffer.allocateDirect(vertices.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply { put(vertices); position(0) }
        }
        indexBuffer = ByteBuffer.allocateDirect(indices.size).apply { put(indices); position(0) }
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, """
            uniform mat4 uMVPMatrix;
            attribute vec4 vPosition;
            void main() { gl_Position = uMVPMatrix * vPosition; }
        """.trimIndent())
        
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, """
            precision mediump float;
            uniform vec4 vColor;
            void main() { gl_FragColor = vColor; }
        """.trimIndent())
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}

// Rain Renderer
class RainRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private val rainDrops = mutableListOf<RainDrop>()
    
    data class RainDrop(var x: Float, var y: Float, var z: Float, val speed: Float)
    
    fun initialize() {
        createRainDrops()
        setupShaders()
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType, intensity: Float) {
        GLES20.glUseProgram(program)
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        
        val rainColor = getRainColor(theme)
        GLES20.glUniform4fv(colorHandle, 1, rainColor, 0)
        
        val numDrops = (rainDrops.size * intensity).toInt()
        for (i in 0 until numDrops) {
            updateAndDrawRainDrop(rainDrops[i], mvpMatrix)
        }
        
        GLES20.glDisable(GLES20.GL_BLEND)
    }
    
    fun updateTheme(theme: ThemeType) { /* Update rain appearance */ }
    
    private fun createRainDrops() {
        rainDrops.clear()
        repeat(100) {
            rainDrops.add(RainDrop(
                Random.nextFloat() * 4f - 2f,
                Random.nextFloat() * 4f + 2f,
                Random.nextFloat() * -2f,
                Random.nextFloat() * 0.05f + 0.02f
            ))
        }
    }
    
    private fun updateAndDrawRainDrop(drop: RainDrop, mvpMatrix: FloatArray) {
        drop.y -= drop.speed
        if (drop.y < -3f) {
            drop.y = 3f
            drop.x = Random.nextFloat() * 4f - 2f
        }
        
        // Draw rain drop as a small line
        val vertices = floatArrayOf(
            drop.x, drop.y, drop.z,
            drop.x, drop.y - 0.1f, drop.z
        )
        
        val vertexBuffer = ByteBuffer.allocateDirect(vertices.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply { put(vertices); position(0) }
        }
        
        GLES20.glEnableVertexAttribArray(positionHandle)
        GLES20.glVertexAttribPointer(positionHandle, 3, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, mvpMatrix, 0)
        GLES20.glDrawArrays(GLES20.GL_LINES, 0, 2)
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    private fun getRainColor(theme: ThemeType): FloatArray {
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(0.0f, 0.5f, 1.0f, 0.8f)
            ThemeType.CHROMA -> floatArrayOf(0.5f, 0.0f, 1.0f, 0.8f)
            else -> floatArrayOf(0.3f, 0.6f, 1.0f, 0.8f)
        }
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, """
            uniform mat4 uMVPMatrix;
            attribute vec4 vPosition;
            void main() { gl_Position = uMVPMatrix * vPosition; }
        """.trimIndent())
        
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, """
            precision mediump float;
            uniform vec4 vColor;
            void main() { gl_FragColor = vColor; }
        """.trimIndent())
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}

// Snow Renderer
class SnowRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private val snowFlakes = mutableListOf<SnowFlake>()
    
    data class SnowFlake(var x: Float, var y: Float, var z: Float, val speed: Float, val size: Float)
    
    fun initialize() {
        createSnowFlakes()
        setupShaders()
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType, intensity: Float) {
        GLES20.glUseProgram(program)
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        
        val snowColor = getSnowColor(theme)
        GLES20.glUniform4fv(colorHandle, 1, snowColor, 0)
        
        val numFlakes = (snowFlakes.size * intensity).toInt()
        for (i in 0 until numFlakes) {
            updateAndDrawSnowFlake(snowFlakes[i], mvpMatrix)
        }
        
        GLES20.glDisable(GLES20.GL_BLEND)
    }
    
    fun updateTheme(theme: ThemeType) { /* Update snow appearance */ }
    
    private fun createSnowFlakes() {
        snowFlakes.clear()
        repeat(80) {
            snowFlakes.add(SnowFlake(
                Random.nextFloat() * 4f - 2f,
                Random.nextFloat() * 4f + 2f,
                Random.nextFloat() * -2f,
                Random.nextFloat() * 0.02f + 0.01f,
                Random.nextFloat() * 0.05f + 0.02f
            ))
        }
    }
    
    private fun updateAndDrawSnowFlake(flake: SnowFlake, mvpMatrix: FloatArray) {
        flake.y -= flake.speed
        flake.x += (Random.nextFloat() - 0.5f) * 0.01f // Slight horizontal drift
        
        if (flake.y < -3f) {
            flake.y = 3f
            flake.x = Random.nextFloat() * 4f - 2f
        }
        
        // Draw snowflake as a small square
        val size = flake.size
        val vertices = floatArrayOf(
            flake.x - size, flake.y + size, flake.z,
            flake.x + size, flake.y + size, flake.z,
            flake.x + size, flake.y - size, flake.z,
            flake.x - size, flake.y - size, flake.z
        )
        
        val indices = byteArrayOf(0, 1, 2, 0, 2, 3)
        
        val vertexBuffer = ByteBuffer.allocateDirect(vertices.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply { put(vertices); position(0) }
        }
        
        val indexBuffer = ByteBuffer.allocateDirect(indices.size).apply { put(indices); position(0) }
        
        GLES20.glEnableVertexAttribArray(positionHandle)
        GLES20.glVertexAttribPointer(positionHandle, 3, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, mvpMatrix, 0)
        GLES20.glDrawElements(GLES20.GL_TRIANGLES, 6, GLES20.GL_UNSIGNED_BYTE, indexBuffer)
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    private fun getSnowColor(theme: ThemeType): FloatArray {
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(1.0f, 0.9f, 0.8f, 0.9f)
            ThemeType.CHROMA -> floatArrayOf(0.9f, 0.8f, 1.0f, 0.9f)
            else -> floatArrayOf(1.0f, 1.0f, 1.0f, 0.9f)
        }
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, """
            uniform mat4 uMVPMatrix;
            attribute vec4 vPosition;
            void main() { gl_Position = uMVPMatrix * vPosition; }
        """.trimIndent())
        
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, """
            precision mediump float;
            uniform vec4 vColor;
            void main() { gl_FragColor = vColor; }
        """.trimIndent())
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}

// Lightning Renderer
class LightningRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private var lightningTimer = 0f
    private var showLightning = false
    
    fun initialize() {
        setupShaders()
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType) {
        lightningTimer += 0.016f // Assuming 60 FPS
        
        if (lightningTimer > 2f) { // Lightning every 2 seconds
            showLightning = true
            lightningTimer = 0f
        }
        
        if (showLightning && lightningTimer < 0.1f) { // Show for 0.1 seconds
            drawLightningBolt(mvpMatrix, theme)
        } else if (lightningTimer > 0.1f) {
            showLightning = false
        }
    }
    
    fun updateTheme(theme: ThemeType) { /* Update lightning appearance */ }
    
    private fun drawLightningBolt(mvpMatrix: FloatArray, theme: ThemeType) {
        GLES20.glUseProgram(program)
        
        // Create a jagged lightning bolt
        val vertices = floatArrayOf(
            0f, 1f, 0f,
            -0.1f, 0.5f, 0f,
            0.1f, 0f, 0f,
            -0.05f, -0.5f, 0f,
            0f, -1f, 0f
        )
        
        val vertexBuffer = ByteBuffer.allocateDirect(vertices.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply { put(vertices); position(0) }
        }
        
        GLES20.glEnableVertexAttribArray(positionHandle)
        GLES20.glVertexAttribPointer(positionHandle, 3, GLES20.GL_FLOAT, false, 0, vertexBuffer)
        
        val lightningColor = getLightningColor(theme)
        GLES20.glUniform4fv(colorHandle, 1, lightningColor, 0)
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, mvpMatrix, 0)
        
        GLES20.glLineWidth(3f)
        GLES20.glDrawArrays(GLES20.GL_LINE_STRIP, 0, 5)
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    private fun getLightningColor(theme: ThemeType): FloatArray {
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(1.0f, 0.8f, 0.0f, 1.0f)
            ThemeType.CHROMA -> floatArrayOf(1.0f, 0.0f, 1.0f, 1.0f)
            else -> floatArrayOf(1.0f, 1.0f, 1.0f, 1.0f)
        }
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, """
            uniform mat4 uMVPMatrix;
            attribute vec4 vPosition;
            void main() { gl_Position = uMVPMatrix * vPosition; }
        """.trimIndent())
        
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, """
            precision mediump float;
            uniform vec4 vColor;
            void main() { gl_FragColor = vColor; }
        """.trimIndent())
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}

// Particle System
class ParticleSystem(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    fun initialize() {
        setupShaders()
    }
    
    fun drawFog(mvpMatrix: FloatArray, theme: ThemeType, density: Float) {
        // Implement fog particle effect
    }
    
    fun update() {
        // Update particle positions and lifecycle
    }
    
    fun updateTheme(theme: ThemeType) {
        // Update particle appearance based on theme
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, """
            uniform mat4 uMVPMatrix;
            attribute vec4 vPosition;
            void main() { gl_Position = uMVPMatrix * vPosition; }
        """.trimIndent())
        
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, """
            precision mediump float;
            uniform vec4 vColor;
            void main() { gl_FragColor = vColor; }
        """.trimIndent())
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}
