package com.andyworks.simpleweather.data.repository

import android.content.Context
import com.andyworks.simpleweather.data.model.ThemeColors
import com.andyworks.simpleweather.data.model.ThemeType
import com.andyworks.simpleweather.data.model.WeatherTheme
import com.google.gson.Gson
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ThemeRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    
    private val themeCache = mutableMapOf<String, WeatherTheme>()
    private val colorCache = mutableMapOf<String, ThemeColors>()
    
    suspend fun getTheme(themeType: ThemeType): WeatherTheme? = withContext(Dispatchers.IO) {
        themeCache[themeType.id] ?: loadThemeFromAssets(themeType)?.also { theme ->
            themeCache[themeType.id] = theme
        }
    }
    
    suspend fun getThemeColors(themeType: ThemeType): ThemeColors? = withContext(Dispatchers.IO) {
        colorCache[themeType.id] ?: loadThemeColorsFromAssets(themeType)?.also { colors ->
            colorCache[themeType.id] = colors
        }
    }
    
    suspend fun getAllAvailableThemes(): List<ThemeType> = withContext(Dispatchers.IO) {
        ThemeType.values().toList()
    }
    
    private fun loadThemeFromAssets(themeType: ThemeType): WeatherTheme? {
        return try {
            val fileName = "themes/${themeType.id}-theme.json"
            val jsonString = context.assets.open(fileName).bufferedReader().use { it.readText() }
            gson.fromJson(jsonString, WeatherTheme::class.java)
        } catch (e: Exception) {
            null
        }
    }
    
    private fun loadThemeColorsFromAssets(themeType: ThemeType): ThemeColors? {
        return try {
            val fileName = "themes/${themeType.id}-colors.json"
            val jsonString = context.assets.open(fileName).bufferedReader().use { it.readText() }
            gson.fromJson(jsonString, ThemeColors::class.java)
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun getThemeAnimationConfig(themeType: ThemeType): Map<String, Any>? = withContext(Dispatchers.IO) {
        try {
            val fileName = "themes/${themeType.id}-animation.json"
            val jsonString = context.assets.open(fileName).bufferedReader().use { it.readText() }
            gson.fromJson(jsonString, Map::class.java) as? Map<String, Any>
        } catch (e: Exception) {
            null
        }
    }
    
    fun getThemeDisplayName(themeType: ThemeType): String {
        return themeType.displayName
    }
    
    fun isThemeUnlocked(themeType: ThemeType): Boolean {
        // In the original app, some themes require purchase
        // For this implementation, we'll make all themes available
        return true
    }
    
    suspend fun preloadAllThemes() = withContext(Dispatchers.IO) {
        ThemeType.values().forEach { themeType ->
            if (!themeCache.containsKey(themeType.id)) {
                loadThemeFromAssets(themeType)?.let { theme ->
                    themeCache[themeType.id] = theme
                }
            }
            if (!colorCache.containsKey(themeType.id)) {
                loadThemeColorsFromAssets(themeType)?.let { colors ->
                    colorCache[themeType.id] = colors
                }
            }
        }
    }
}
