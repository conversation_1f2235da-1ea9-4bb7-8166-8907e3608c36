package com.andyworks.simpleweather.data.model

import kotlinx.serialization.Serializable

@Serializable
data class WeatherResponse(
    val current: CurrentWeather,
    val daily: List<DailyWeather>,
    val hourly: List<HourlyWeather>,
    val hour: List<HourWeather>
)

@Serializable
data class CurrentWeather(
    val cloudCover: Double,
    val feelsLike: TemperatureValue,
    val lat: Double,
    val lon: Double,
    val temp: TemperatureValue,
    val dewPoint: TemperatureValue,
    val timestamp: Long,
    val windDirection: Double,
    val weatherCode: String,
    val windSpeed: WindValue,
    val windGust: WindValue,
    val pressure: Double,
    val humidity: Double,
    val uvi: Double,
    val visibility: Double,
    val rain: Double,
    val snow: Double,
    val precipitationType: String,
    val epaIndex: Int,
    val pollen: PollenData
)

@Serializable
data class DailyWeather(
    val cloudCover: Double,
    val dewPoint: TemperatureValue,
    val humidity: Double,
    val pressure: Double,
    val rain: Double,
    val snow: Double,
    val uvi: Int,
    val lat: Double,
    val lon: Double,
    val minFeelsLike: TemperatureValue,
    val minTemp: TemperatureValue,
    val maxFeelsLike: TemperatureValue,
    val maxTemp: TemperatureValue,
    val moonPhase: String,
    val moonFraction: Double,
    val sunrise: Long?,
    val sunset: Long?,
    val precipitationProbability: Int,
    val precipitationType: String,
    val timestamp: Long,
    val windDirection: Double,
    val weatherCode: String,
    val windSpeed: WindSpeedRange,
    val epaIndex: Int
)

@Serializable
data class HourlyWeather(
    val dewPoint: TemperatureValue,
    val humidity: Double,
    val pressure: Double,
    val rain: Double,
    val snow: Double,
    val visibility: Double,
    val cloudCover: Double,
    val lat: Double,
    val lon: Double,
    val feelsLike: TemperatureValue,
    val temp: TemperatureValue,
    val precipitationProbability: Int,
    val precipitationType: String,
    val timestamp: Long,
    val weatherCode: String,
    val windDirection: Double,
    val windSpeed: WindValue
)

@Serializable
data class HourWeather(
    val lat: Double? = null,
    val lon: Double? = null,
    val feelsLike: TemperatureValue? = null,
    val temp: TemperatureValue? = null,
    val precipitation: PrecipitationValue,
    val precipitationType: String,
    val timestamp: Long,
    val windDirection: Double? = null,
    val windSpeed: WindValue? = null
)

@Serializable
data class TemperatureValue(
    val value: Double,
    val units: String,
    val timestamp: Long? = null
)

@Serializable
data class WindValue(
    val value: Double,
    val units: String,
    val timestamp: Long
)

@Serializable
data class WindSpeedRange(
    val min: WindValue,
    val max: WindValue
)

@Serializable
data class PrecipitationValue(
    val value: Double,
    val units: String
)

@Serializable
data class PollenData(
    val grass: Int,
    val ragweed: Int,
    val tree: Int,
    val mold: Int
)

// Weather codes enum for type safety
enum class WeatherCode(val code: String) {
    CLEAR("clear"),
    MOSTLY_CLEAR("mostlyClear"),
    PARTLY_CLOUDY("partlyCloudy"),
    MOSTLY_CLOUDY("mostlyCloudy"),
    CLOUDY("cloudy"),
    FOG("fog"),
    FOG_LIGHT("fogLight"),
    DRIZZLE("drizzle"),
    RAIN_LIGHT("rainLight"),
    RAIN("rain"),
    RAIN_HEAVY("rainHeavy"),
    SNOW_LIGHT("snowLight"),
    SNOW("snow"),
    SNOW_HEAVY("snowHeavy"),
    FLURRIES("flurries"),
    FREEZING_DRIZZLE("freezingDrizzle"),
    FREEZING_RAIN_LIGHT("freezingRainLight"),
    FREEZING_RAIN("freezingRain"),
    FREEZING_RAIN_HEAVY("freezingRainHeavy"),
    ICE_PELLETS_LIGHT("icePelletsLight"),
    ICE_PELLETS("icePellets"),
    ICE_PELLETS_HEAVY("icePelletsHeavy"),
    THUNDERSTORM("thunderstorm"),
    SMOKE("smoke"),
    HAZE("haze");

    companion object {
        fun fromString(code: String): WeatherCode? {
            return values().find { it.code == code }
        }
    }
}

// Moon phases enum
enum class MoonPhase(val phase: String) {
    NEW("new"),
    WAXING_CRESCENT("waxingCrescent"),
    FIRST_QUARTER("firstQuarter"),
    WAXING_GIBBOUS("waxingGibbous"),
    FULL("full"),
    WANING_GIBBOUS("waningGibbous"),
    THIRD_QUARTER("thirdQuarter"),
    WANING_CRESCENT("waningCrescent");

    companion object {
        fun fromString(phase: String): MoonPhase? {
            return values().find { it.phase == phase }
        }
    }
}
