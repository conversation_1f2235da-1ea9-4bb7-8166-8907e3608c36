package com.andyworks.simpleweather.graphics

import android.content.Context
import android.opengl.GLES20
import android.opengl.Matrix
import com.andyworks.simpleweather.data.model.ThemeType
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

class CloudRenderer(private val context: Context) {
    
    private var program: Int = 0
    private var positionHandle: Int = 0
    private var colorHandle: Int = 0
    private var mvpMatrixHandle: Int = 0
    
    private lateinit var vertexBuffer: FloatBuffer
    private lateinit var indexBuffer: ByteBuffer
    
    private val clouds = mutableListOf<Cloud>()
    private var currentTheme = ThemeType.NORMAL
    
    private val vertexShaderCode = """
        uniform mat4 uMVPMatrix;
        attribute vec4 vPosition;
        void main() {
            gl_Position = uMVPMatrix * vPosition;
        }
    """.trimIndent()
    
    private val fragmentShaderCode = """
        precision mediump float;
        uniform vec4 vColor;
        void main() {
            gl_FragColor = vColor;
        }
    """.trimIndent()
    
    data class Cloud(
        var x: Float,
        var y: Float,
        var z: Float,
        val scale: Float,
        val speed: Float,
        val vertices: FloatArray,
        val indices: ByteArray
    )
    
    fun initialize() {
        createClouds()
        setupShaders()
    }
    
    fun draw(mvpMatrix: FloatArray, theme: ThemeType, density: Float) {
        GLES20.glUseProgram(program)
        
        // Enable blending for cloud transparency
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        
        val cloudColor = getCloudColor(theme, density)
        GLES20.glUniform4fv(colorHandle, 1, cloudColor, 0)
        
        // Draw each cloud
        val numCloudsToShow = (clouds.size * density).toInt()
        for (i in 0 until numCloudsToShow) {
            val cloud = clouds[i]
            updateCloud(cloud)
            drawCloud(cloud, mvpMatrix)
        }
        
        GLES20.glDisable(GLES20.GL_BLEND)
    }
    
    fun updateTheme(theme: ThemeType) {
        currentTheme = theme
    }
    
    fun onTouch(x: Float, y: Float) {
        // Add interaction - maybe clouds disperse or change shape
    }
    
    private fun createClouds() {
        clouds.clear()
        
        // Create multiple clouds with different sizes and positions
        for (i in 0 until 8) {
            val x = Random.nextFloat() * 4f - 2f
            val y = Random.nextFloat() * 2f - 1f
            val z = Random.nextFloat() * -2f - 1f
            val scale = Random.nextFloat() * 0.3f + 0.2f
            val speed = Random.nextFloat() * 0.01f + 0.005f
            
            val (vertices, indices) = createCloudGeometry(scale)
            
            clouds.add(Cloud(x, y, z, scale, speed, vertices, indices))
        }
    }
    
    private fun createCloudGeometry(scale: Float): Pair<FloatArray, ByteArray> {
        val vertices = mutableListOf<Float>()
        val indices = mutableListOf<Byte>()
        
        // Create cloud as multiple overlapping circles
        val numCircles = 5
        val baseRadius = 0.2f * scale
        
        for (circleIndex in 0 until numCircles) {
            val centerX = (Random.nextFloat() - 0.5f) * 0.4f * scale
            val centerY = (Random.nextFloat() - 0.5f) * 0.2f * scale
            val radius = baseRadius * (0.7f + Random.nextFloat() * 0.6f)
            
            val segments = 12
            val startVertexIndex = vertices.size / 3
            
            // Center vertex
            vertices.addAll(listOf(centerX, centerY, 0f))
            
            // Circle vertices
            for (i in 0 until segments) {
                val angle = 2.0 * Math.PI * i / segments
                val x = centerX + (radius * cos(angle)).toFloat()
                val y = centerY + (radius * sin(angle)).toFloat()
                vertices.addAll(listOf(x, y, 0f))
            }
            
            // Circle indices
            for (i in 0 until segments) {
                val center = startVertexIndex.toByte()
                val current = (startVertexIndex + i + 1).toByte()
                val next = (startVertexIndex + (i + 1) % segments + 1).toByte()
                indices.addAll(listOf(center, current, next))
            }
        }
        
        return Pair(vertices.toFloatArray(), indices.toByteArray())
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode)
        
        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }
        
        positionHandle = GLES20.glGetAttribLocation(program, "vPosition")
        colorHandle = GLES20.glGetUniformLocation(program, "vColor")
        mvpMatrixHandle = GLES20.glGetUniformLocation(program, "uMVPMatrix")
    }
    
    private fun updateCloud(cloud: Cloud) {
        // Move cloud horizontally
        cloud.x += cloud.speed
        
        // Wrap around screen
        if (cloud.x > 3f) {
            cloud.x = -3f
        }
    }
    
    private fun drawCloud(cloud: Cloud, mvpMatrix: FloatArray) {
        // Create vertex buffer for this cloud
        val vertexBuffer = ByteBuffer.allocateDirect(cloud.vertices.size * 4).run {
            order(ByteOrder.nativeOrder())
            asFloatBuffer().apply {
                put(cloud.vertices)
                position(0)
            }
        }
        
        val indexBuffer = ByteBuffer.allocateDirect(cloud.indices.size).apply {
            put(cloud.indices)
            position(0)
        }
        
        // Set up model matrix for this cloud
        val modelMatrix = FloatArray(16)
        Matrix.setIdentityM(modelMatrix, 0)
        Matrix.translateM(modelMatrix, 0, cloud.x, cloud.y, cloud.z)
        
        val finalMvpMatrix = FloatArray(16)
        Matrix.multiplyMM(finalMvpMatrix, 0, mvpMatrix, 0, modelMatrix, 0)
        
        // Enable vertex array
        GLES20.glEnableVertexAttribArray(positionHandle)
        
        // Prepare the coordinate data
        GLES20.glVertexAttribPointer(
            positionHandle, 3,
            GLES20.GL_FLOAT, false,
            0, vertexBuffer
        )
        
        // Apply the projection and view transformation
        GLES20.glUniformMatrix4fv(mvpMatrixHandle, 1, false, finalMvpMatrix, 0)
        
        // Draw the cloud
        GLES20.glDrawElements(
            GLES20.GL_TRIANGLES, cloud.indices.size,
            GLES20.GL_UNSIGNED_BYTE, indexBuffer
        )
        
        // Disable vertex array
        GLES20.glDisableVertexAttribArray(positionHandle)
    }
    
    private fun getCloudColor(theme: ThemeType, density: Float): FloatArray {
        val alpha = 0.6f + density * 0.4f
        
        return when (theme) {
            ThemeType.ANDY -> floatArrayOf(0.4f, 0.2f, 0.0f, alpha)
            ThemeType.CHROMA -> floatArrayOf(0.8f, 0.4f, 0.9f, alpha)
            ThemeType.DEPTH -> floatArrayOf(0.3f, 0.3f, 0.3f, alpha)
            ThemeType.GRAPHITE -> floatArrayOf(0.2f, 0.2f, 0.2f, alpha)
            else -> floatArrayOf(0.8f, 0.8f, 0.8f, alpha) // Default light gray
        }
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, shaderCode)
            GLES20.glCompileShader(shader)
        }
    }
}
