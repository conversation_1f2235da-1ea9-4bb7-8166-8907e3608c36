package com.andyworks.simpleweather.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.andyworks.simpleweather.data.model.ThemeType
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

@Singleton
class PreferencesRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private object PreferencesKeys {
        val SELECTED_THEME = stringPreferencesKey("selected_theme")
        val TEMPERATURE_UNIT = stringPreferencesKey("temperature_unit")
        val WIND_SPEED_UNIT = stringPreferencesKey("wind_speed_unit")
        val PRESSURE_UNIT = stringPreferencesKey("pressure_unit")
        val PRECIPITATION_UNIT = stringPreferencesKey("precipitation_unit")
        val DATA_SOURCE = stringPreferencesKey("data_source")
        val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        val HAPTIC_FEEDBACK_ENABLED = booleanPreferencesKey("haptic_feedback_enabled")
        val AUTO_LOCATION = booleanPreferencesKey("auto_location")
        val BACKGROUND_REFRESH = booleanPreferencesKey("background_refresh")
        val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")
        val SEVERE_WEATHER_ALERTS = booleanPreferencesKey("severe_weather_alerts")
        val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
    }
    
    // Theme preferences
    suspend fun setSelectedTheme(theme: ThemeType) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.SELECTED_THEME] = theme.id
        }
    }
    
    fun getSelectedTheme(): Flow<ThemeType> {
        return context.dataStore.data.map { preferences ->
            val themeId = preferences[PreferencesKeys.SELECTED_THEME] ?: ThemeType.NORMAL.id
            ThemeType.fromId(themeId) ?: ThemeType.NORMAL
        }
    }
    
    suspend fun getSelectedThemeSync(): ThemeType {
        return getSelectedTheme().first()
    }
    
    // Unit preferences
    suspend fun setTemperatureUnit(unit: TemperatureUnit) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.TEMPERATURE_UNIT] = unit.name
        }
    }
    
    fun getTemperatureUnit(): Flow<TemperatureUnit> {
        return context.dataStore.data.map { preferences ->
            val unit = preferences[PreferencesKeys.TEMPERATURE_UNIT] ?: TemperatureUnit.CELSIUS.name
            TemperatureUnit.valueOf(unit)
        }
    }
    
    suspend fun getTemperatureUnitSync(): String {
        val unit = getTemperatureUnit().first()
        return when (unit) {
            TemperatureUnit.CELSIUS -> "metric"
            TemperatureUnit.FAHRENHEIT -> "imperial"
        }
    }
    
    suspend fun setWindSpeedUnit(unit: WindSpeedUnit) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.WIND_SPEED_UNIT] = unit.name
        }
    }
    
    fun getWindSpeedUnit(): Flow<WindSpeedUnit> {
        return context.dataStore.data.map { preferences ->
            val unit = preferences[PreferencesKeys.WIND_SPEED_UNIT] ?: WindSpeedUnit.KMH.name
            WindSpeedUnit.valueOf(unit)
        }
    }
    
    // Data source preferences
    suspend fun setSelectedDataSource(dataSource: DataSource) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.DATA_SOURCE] = dataSource.name
        }
    }
    
    fun getSelectedDataSource(): Flow<DataSource> {
        return context.dataStore.data.map { preferences ->
            val source = preferences[PreferencesKeys.DATA_SOURCE] ?: DataSource.ANDY_WORKS.name
            DataSource.valueOf(source)
        }
    }
    
    suspend fun getSelectedDataSourceSync(): DataSource {
        return getSelectedDataSource().first()
    }
    
    // Audio preferences
    suspend fun setSoundEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.SOUND_ENABLED] = enabled
        }
    }
    
    fun isSoundEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[PreferencesKeys.SOUND_ENABLED] ?: true
        }
    }
    
    suspend fun setHapticFeedbackEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.HAPTIC_FEEDBACK_ENABLED] = enabled
        }
    }
    
    fun isHapticFeedbackEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[PreferencesKeys.HAPTIC_FEEDBACK_ENABLED] ?: true
        }
    }
    
    // Location preferences
    suspend fun setAutoLocationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.AUTO_LOCATION] = enabled
        }
    }
    
    fun isAutoLocationEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[PreferencesKeys.AUTO_LOCATION] ?: true
        }
    }
    
    // App state
    suspend fun setFirstLaunch(isFirstLaunch: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.FIRST_LAUNCH] = isFirstLaunch
        }
    }
    
    fun isFirstLaunch(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[PreferencesKeys.FIRST_LAUNCH] ?: true
        }
    }
}

enum class TemperatureUnit {
    CELSIUS,
    FAHRENHEIT
}

enum class WindSpeedUnit {
    KMH,
    MPH,
    MS
}

enum class PressureUnit {
    HPA,
    INHG,
    MBAR
}

enum class PrecipitationUnit {
    MM,
    INCHES
}
