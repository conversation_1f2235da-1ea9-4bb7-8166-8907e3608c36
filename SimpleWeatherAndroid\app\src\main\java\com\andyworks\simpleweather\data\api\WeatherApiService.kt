package com.andyworks.simpleweather.data.api

import com.andyworks.simpleweather.data.model.WeatherResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

interface WeatherApiService {
    
    @GET("weather/current")
    suspend fun getCurrentWeather(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("units") units: String = "metric"
    ): Response<WeatherResponse>
    
    @GET("weather/forecast")
    suspend fun getWeatherForecast(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("days") days: Int = 7,
        @Query("units") units: String = "metric"
    ): Response<WeatherResponse>
    
    @GET("weather/hourly")
    suspend fun getHourlyForecast(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("hours") hours: Int = 24,
        @Query("units") units: String = "metric"
    ): Response<WeatherResponse>
}

// Alternative weather API services for different data sources
interface OpenWeatherMapService {
    
    @GET("data/2.5/weather")
    suspend fun getCurrentWeather(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric"
    ): Response<OpenWeatherCurrentResponse>
    
    @GET("data/2.5/forecast")
    suspend fun getForecast(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric"
    ): Response<OpenWeatherForecastResponse>
}

interface AccuWeatherService {
    
    @GET("locations/v1/cities/geoposition/search")
    suspend fun getLocationKey(
        @Query("apikey") apiKey: String,
        @Query("q") coordinates: String
    ): Response<AccuWeatherLocationResponse>
    
    @GET("currentconditions/v1/{locationKey}")
    suspend fun getCurrentConditions(
        @Query("apikey") apiKey: String,
        @Query("details") details: Boolean = true
    ): Response<List<AccuWeatherCurrentResponse>>
    
    @GET("forecasts/v1/daily/5day/{locationKey}")
    suspend fun getDailyForecast(
        @Query("apikey") apiKey: String,
        @Query("details") details: Boolean = true,
        @Query("metric") metric: Boolean = true
    ): Response<AccuWeatherForecastResponse>
}

// Response models for different APIs
data class OpenWeatherCurrentResponse(
    val coord: Coordinates,
    val weather: List<WeatherDescription>,
    val main: MainWeatherData,
    val wind: WindData,
    val clouds: CloudData,
    val dt: Long,
    val sys: SystemData,
    val timezone: Int,
    val id: Int,
    val name: String
)

data class OpenWeatherForecastResponse(
    val list: List<ForecastItem>,
    val city: CityInfo
)

data class AccuWeatherLocationResponse(
    val Key: String,
    val LocalizedName: String,
    val Country: CountryInfo,
    val AdministrativeArea: AdministrativeAreaInfo,
    val GeoPosition: GeoPosition
)

data class AccuWeatherCurrentResponse(
    val LocalObservationDateTime: String,
    val EpochTime: Long,
    val WeatherText: String,
    val WeatherIcon: Int,
    val Temperature: TemperatureInfo,
    val RealFeelTemperature: TemperatureInfo,
    val RelativeHumidity: Int,
    val Wind: AccuWeatherWind,
    val UVIndex: Int,
    val Visibility: ValueUnit,
    val CloudCover: Int
)

data class AccuWeatherForecastResponse(
    val DailyForecasts: List<AccuWeatherDailyForecast>
)

// Supporting data classes
data class Coordinates(val lon: Double, val lat: Double)
data class WeatherDescription(val id: Int, val main: String, val description: String, val icon: String)
data class MainWeatherData(val temp: Double, val feels_like: Double, val temp_min: Double, val temp_max: Double, val pressure: Int, val humidity: Int)
data class WindData(val speed: Double, val deg: Int, val gust: Double?)
data class CloudData(val all: Int)
data class SystemData(val country: String, val sunrise: Long, val sunset: Long)
data class ForecastItem(val dt: Long, val main: MainWeatherData, val weather: List<WeatherDescription>, val wind: WindData)
data class CityInfo(val id: Int, val name: String, val coord: Coordinates, val country: String)
data class CountryInfo(val ID: String, val LocalizedName: String)
data class AdministrativeAreaInfo(val ID: String, val LocalizedName: String)
data class GeoPosition(val Latitude: Double, val Longitude: Double)
data class TemperatureInfo(val Metric: ValueUnit, val Imperial: ValueUnit)
data class ValueUnit(val Value: Double, val Unit: String)
data class AccuWeatherWind(val Speed: ValueUnit, val Direction: DirectionInfo)
data class DirectionInfo(val Degrees: Int, val Localized: String)
data class AccuWeatherDailyForecast(
    val Date: String,
    val EpochDate: Long,
    val Temperature: DailyTemperature,
    val Day: DayNightInfo,
    val Night: DayNightInfo
)
data class DailyTemperature(val Minimum: ValueUnit, val Maximum: ValueUnit)
data class DayNightInfo(val Icon: Int, val IconPhrase: String, val HasPrecipitation: Boolean)
